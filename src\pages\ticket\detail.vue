<route lang="jsonc">{
    "layout": "custom",
    "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "景区详情"
    }
}</route>

<script lang="ts" setup>
import { getScenicInfo } from '@/api/scenic';
import { getTicketTypeList, getTicketList } from '@/api/ticket';
import { useTenantStore } from '@/store';
import { useSafeAreaInsets } from '@/hooks/useSafeAreaInsets';
// import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
const tenantStore = useTenantStore()
const insets = useSafeAreaInsets()
onLoad((options) => {
    getInfo(options.id)
    getType(options.id)
})
const infoData = ref<any>({})
const typeData = ref<any>([])
const ticketList = ref<any>([])
const getInfo = async (id: any) => {
    const res = await getScenicInfo({
        scenicId: id,
        tenantId: tenantStore.tenantInfo.tenantId
    })
    if (res.code === 200) {
        infoData.value = res.data
        infoData.value.scenicImage = [...infoData.value.scenicImage.split(','), ...infoData.value.scenicVideo.split(',')]
    }
}
const getType = async (id: any) => {
    const res: any = await getTicketTypeList({ tenantId: tenantStore.tenantInfo.tenantId, scenicId: id })
    if (res.code === 200) {
        typeData.value = [{ id: 0, typeName: '全部' }, ...res.data]
        getList()
    }
}
const currentType = ref(0)
const changeType = ({ index }: any) => {
    currentType.value = index
    getList()
}
const getList = async () => {
    const res = await getTicketList({
        scenicId: infoData.value.id,
        typeId: typeData.value[currentType.value].id,
        tenantId: tenantStore.tenantInfo.tenantId
    })
    if (res.code === 200) {
        ticketList.value = res.data
    }
}
const showIntroduce = ref(false)

// 监听页面滚动，需要时必填，在layouts的custom.vue中声明接收页面滚动事件
onPageScroll((e: any) => {
    console.log(e, 'details')
})
defineExpose({
    title: '景区门票'
})
const openMap = () => {
    uni.openLocation({
        latitude: Number(infoData.value.latitude),  // 纬度，范围为-90~90，负数表示南纬。
        longitude: Number(infoData.value.longitude), // 经度，范围为-180~180，负数表示西经。
        name: infoData.value.scenicName,
        address: infoData.value.address,
        success: (res) => {
            console.log(res);
        },
        fail: (fail) => {
            console.log('fail ', fail)
        },
    });
}

const openPhone = () => {
    uni.makePhoneCall({
        phoneNumber: infoData.value.contact
    })
}
</script>

<template :title="infoData.scenicName">
    <view class="relative">
        <view class="flex">
            <wd-swiper :list="infoData.scenicImage" height="210px" custom-class="w-full rounded-0"
                :indicator="{ type: 'dots-bar' }" stopAutoplayWhenVideoPlay :autoplayVideo="false" :muted="false">
            </wd-swiper>
        </view>
        <view class="bg-[#f6f6f6] dark:bg-black">
            <view class="p-3 bg-white dark:bg-dark mb-3">
                <view class="flex items-center justify-between mb-3">
                    <view class="text-lg font-bold">{{ infoData.scenicName }}</view>
                    <wd-button type="primary" size="small">去购票</wd-button>
                </view>
                <view class="text-sm flex justify-between items-center mb-3">
                    <view class="text-gray-500">开放时间：{{ infoData.businessHours }}</view>
                    <view class="text-primary" @click="showIntroduce = true">
                        <text>景区介绍</text>
                        <wd-icon name="arrow-right" size="22px"></wd-icon>
                    </view>
                </view>
                <view class="bg-[url(https://ticketoss.yunvip123.com/ticket/mapbg.png)] bg-no-repeat bg-center bg-cover
                            h-[48px] flex items-center justify-between px-2">
                    <view class="text-xs">
                        {{ infoData.address }}
                    </view>
                    <view class="flex-shrink-0 flex items-center gap-2">
                        <view class="text-xs text-center " @click="openMap">
                            <view
                                class="bg-white dark:bg-dark rounded-full p-1 flex items-center justify-center mb-0.5">
                                <text class="i-carbon-map" />
                            </view>
                            <text class="text-2xs">地图</text>
                        </view>
                        <view class="text-xs text-center" @click="openPhone">
                            <view
                                class="bg-white dark:bg-dark rounded-full p-1 flex items-center justify-center mb-0.5">
                                <text class="i-carbon-phone" />
                            </view>
                            <text class="text-2xs">电话</text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="w-full">
                <wd-sticky :offset-top="44 + insets.top">
                    <view class="w-vw">
                        <wd-tabs v-model="currentType" :auto-line-width="true" :slidable-num="5" @change="changeType">
                            <wd-tab v-for="item in typeData" :key="item.id" :title="item.typeName"></wd-tab>
                        </wd-tabs>
                    </view>
                </wd-sticky>
                <view class="p-3">
                    <view class="bg-white dark:bg-dark p-2 rounded mb-3 flex gap-3" v-for="item in ticketList"
                        :key="item.id">
                        <view class="">
                            <wd-img :src="item.ticketCover" width="80" height="80" radius="5"
                                mode="aspectFill"></wd-img>
                        </view>
                        <view class="flex-1 flex flex-col justify-between">
                            <view>{{ item.ticketName }}</view>
                            <view v-if="item.ticketTags && item.ticketTags.length">
                                <wd-tag v-for="tag in item.ticketTags" :key="tag" type="primary" plain
                                    custom-class="mr-1">{{ tag }}</wd-tag>
                            </view>
                            <view class="flex justify-between items-center">
                                <view>
                                    <text class="text-sm text-danger mr-3">￥{{ item.sellingPrice }}</text>
                                    <text class="text-xs text-gray-500 line-through">￥{{ item.marketPrice }}</text>
                                </view>
                                <wd-button type="primary" size="small">购买</wd-button>
                            </view>
                        </view>
                    </view>
                    <wd-status-tip image="content" tip="暂无门票" v-if="ticketList.length === 0" />
                </view>
            </view>
            <wd-action-sheet v-model="showIntroduce" title="景区介绍" @close="showIntroduce = false">
                <view class="px-3">
                    <rich-text :nodes="infoData.introduce"></rich-text>
                </view>
            </wd-action-sheet>
        </view>
    </view>
</template>

<style lang="scss">
swiper {
    border-radius: 0 !important;
}
</style>
