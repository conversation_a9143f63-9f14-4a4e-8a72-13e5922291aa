<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "退款进度"
    }
}</route>

<script lang="ts" setup>
import { getTicketOrderRefundInfo } from '@/api/ticket';
import { useEnumStore } from '@/store';
const enumStore = useEnumStore()
onLoad((options) => {
    getRefundData(options.id)
})

const refundData = ref<any>()
const getRefundData = async (id: any) => {
    const res: any = await getTicketOrderRefundInfo({
        orderId: id
    })
    if (res.code === 200) {
        refundData.value = res.data
        activeNames.value = res.data.map(item => item.id.toString())
    }
}
const goRefundDetail = (item: any) => {
    uni.navigateTo({
        url: `/pages/ticketOrder/refundDetail?id=${item.id}`
    })
}

const activeNames = ref([])
const filterStatus = (status: any) => {
    return enumStore.enumData?.orderRefundStatus.list.find(item => item.value === status)?.label
}
</script>

<template>
    <view class="p-3">
        <view class="bg-white dark:bg-dark rounded p-2 mb-3" v-for="(item, index) in refundData" :key="index">
            <view class="mb-1" @click="goRefundDetail(item)">
                <view class="font-bold mb-1">{{ filterStatus(item.refundStatus) }}</view>
                <view class="text-sm text-gray-500">退款单号：{{ item.refundNo }}</view>
            </view>
            <!-- <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider> -->
            <wd-collapse v-model="activeNames">
                <wd-collapse-item :name="item.id.toString()" custom-body-class="!px-0 !py-1">
                    <template #title="{ expanded, disabled, isFirst }">
                        <view class="flex justify-between items-center text-gray-700">
                            <view class="text-sm ">退款金额：￥{{ item.refundPrice }}（{{ item.refundNum }}张）</view>
                            <view class="text-sm">{{ expanded ? '收起' : '展开' }}
                                <wd-icon :name="expanded ? 'arrow-up' : 'arrow-down'" size="14"></wd-icon>
                            </view>
                        </view>
                    </template>
                    <view @click="goRefundDetail(item)">
                        <view class="mb-2 text-gray-700">手续费：￥{{ item.refundFeePrice }}</view>
                        <view class="p-1">
                            <wd-steps :active="item.refundStatus == 3 ? 2 : 3" vertical>
                                <wd-step title="申请退款" :description="item.applyTime" />
                                <wd-step title="退款待审核" description=" " />
                                <wd-step
                                    :title="filterStatus(item.refundStatus) + `${item.refundStatus == 4 ? ', 资金已原路返回' : ''}`"
                                    :description="item.refundTime || ' '"
                                    :status="[2, 5].includes(item.refundStatus) ? 'error' : 'process'" />
                            </wd-steps>
                        </view>
                    </view>
                </wd-collapse-item>
            </wd-collapse>
        </view>
    </view>
</template>

<style lang="scss" scoped>
::v-deep .wd-collapse-item__header {
    padding: 12rpx 0 !important;

    &::after {
        display: none !important;
    }
}
</style>
