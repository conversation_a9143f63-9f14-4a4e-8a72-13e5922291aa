<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "退款详情"
    }
}</route>

<script lang="ts" setup>
import { getRefundInfo } from '@/api/ticket';
import { useEnumStore } from '@/store';
const enumStore = useEnumStore()

onLoad((options) => {
    getRefundData(options.id)
})
const refundInfo = ref<any>({});
const getRefundData = async (id) => {
    const res = await getRefundInfo({ refundId: id });
    if (res.code === 200) {
        refundInfo.value = res.data
    }
}
const filterStatus = (status: any) => {
    let obj = { label: '', color: 'default' }
    obj.label = enumStore.enumData?.orderRefundStatus.list.find(item => item.value === status)?.label
    if ([2].includes(status)) {
        obj.color = 'text-warning'
    } else if ([4].includes(status)) {
        obj.color = 'text-success'
    } else if ([8].includes(status)) {
        obj.color = 'text-danger'
    } else {
        obj.color = 'text-info'
    }
    return obj
}

</script>

<template>
    <view class="p-3">
        <view class="bg-white dark:bg-dark rounded p-2 mb-3">
            <view class="text-sm flex justify-between items-center">
                <view class="font-bold">退款进度</view>
                <view :class="`${filterStatus(refundInfo.refundStatus).color}`">{{
                    filterStatus(refundInfo.refundStatus).label }}
                </view>
            </view>
            <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
            <view class="px-1 text-sm mb-2">
                <view class="mb-1">
                    <text class="text-gray-500 mr-2">退款金额</text>￥{{ refundInfo.refundPrice }}
                </view>
                <view class="mb-1">
                    <text class="text-gray-500 mr-2">手续费</text>￥{{ refundInfo.refundFeePrice }}
                </view>
            </view>
            <view class="p-1">
                <wd-steps :active="refundInfo.refundStatus == 3 ? 2 : 3" vertical>
                    <wd-step title="申请退款" :description="refundInfo.applyTime" />
                    <wd-step title="退款待审核" description=" " />
                    <wd-step
                        :title="filterStatus(refundInfo.refundStatus).label + `${refundInfo.refundStatus == 4 ? ', 资金已原路返回' : ''}`"
                        :description="refundInfo.refundTime || ' '"
                        :status="[2, 5].includes(refundInfo.refundStatus) ? 'error' : 'process'" />
                </wd-steps>
            </view>
        </view>
        <view class="bg-white dark:bg-dark rounded p-2 mb-3">
            <view class="text-sm font-bold">退款信息</view>
            <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
            <view class="px-1 text-sm">
                <view class="mb-1">
                    <text class="text-gray-500 mr-2">退款单号</text>{{ refundInfo.refundNo }}
                </view>
                <view class="mb-1">
                    <text class="text-gray-500 mr-2">申请时间</text>{{ refundInfo.applyTime }}
                </view>
                <view class="mb-1">
                    <text class="text-gray-500 mr-2">申请原因</text>{{ refundInfo.refundReason }}
                </view>
            </view>
        </view>
    </view>
</template>

<style lang="scss" scoped></style>
