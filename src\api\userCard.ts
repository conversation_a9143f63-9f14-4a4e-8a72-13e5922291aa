import { http } from '@/http/http'

// 获取游玩卡/畅游卡列表
export function getUserCardList(params: any) {
    return http.get('/tur/userCard/list', params)
}

// 获取单个卡片信息
export function getUserCardInfo(params: any) {
    return http.get('/tur/userCard/info', params)
}

// 获取卡核销记录
export function getUserCardVerifyLog(params: any) {
    return http.get('/tur/userCard/cardVerificationRecord', params)
}

// 激活卡
export function activeUserCard(data: any) {
    return http.post('/tur/userCard/activation', data)
}

// 获取卡的权益
export function getUserCardEquities(params: any) {
    return http.get('/tur/userCard/equities', params)
}

// 修改卡持有人的人脸信息
export function updateUserCardFaceInfo(data: any) {
    return http.post('/tur/userCard/resetFace', data)
}