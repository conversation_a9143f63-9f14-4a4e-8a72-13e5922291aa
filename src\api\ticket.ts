import { http } from '@/http/http'

/**门票列表相关接口*/

// 获取推荐门票
export function getRecommendList(params: any) {
    return http.get('/tur/ticket/recommendList', params)
}
// 获取门票类型
export function getTicketTypeList(params: any) {
    return http.get('/tur/ticket/type', params)
}
// 获取门票列表
export function getTicketList(params: any) {
    return http.get('/tur/ticket/list', params)
}
// 获取门票详情
export function getTicketInfo(params: any) {
    return http.get('/tur/ticket/info', params)
}

/**门票下单相关接口 */

// 获取价格日期信息
export function getCalendarInfo(params: any) {
    return http.get('/tur/ticket/calendarInfo', params)
}
// 获取分时预约配置信息
export function getPeriodInfo(params: any) {
    return http.get('/tur/ticket/periodInfo', params)
}
// 获取可用优惠券信息
export function getCouponInfo(params: any) {
    return http.get('/tur/ticket/couponInfo', params)
}
// 创建订单
export function createTicketOrder(data: any) {
    return http.post('/tur/ticket/createOrder', data)
}
// 获取订单支付结果
export function getTicketOrderPayQuery(params: any) {
    return http.get('/tur/ticket/payQuery', params)
}


/**门票订单相关接口 */

// 获取门票订单列表
export function getTicketOrderList(params: any) {
    return http.get('/tur/ticketOrder/list', params)
}
// 获取门票订单信息
export function getTicketOrderInfo(params: any) {
    return http.get('/tur/ticketOrder/info', params)
}
// 门票订单继续支付
export function ticketOrderToPay(data: any) {
    return http.post('/tur/ticketOrder/toPay', data)
}
// 取消门票订单
export function cancelTicketOrder(data: any) {
    return http.post('/tur/ticketOrder/cancel', data)
}
// 删除门票订单
export function deleteTicketOrder(data: any) {
    return http.post('/tur/ticketOrder/delete', data)
}
// 门票订单退款申请
export function refundTicketOrder(data: any) {
    return http.post('/tur/ticketOrder/refund', data)
}
// 门票订单退款信息
export function getTicketOrderRefundInfo(params: any) {
    return http.get('/tur/ticketOrder/orderRefundInfo', params)
}
// 门票订单退款列表
export function getTicketOrderRefundList(params: any) {
    return http.get('/tur/ticketOrder/refundList', params)
}
// 退款单信息
export function getRefundInfo(params: any) {
    return http.get('/tur/ticketOrder/refundInfo', params)
}