<route lang="jsonc">{
    "layout": "custom",
    "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "景点列表"
    }
}</route>

<script lang="ts" setup>
import { getScenicList, getScenicSpotList } from '@/api/scenic';
import { useTenantStore } from '@/store';
import { useSafeAreaInsets } from '@/hooks/useSafeAreaInsets';
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
const scrollRef = ref(null)
UseZPaging(scrollRef)

defineExpose({
    title: '景点列表'
})
const tenantStore = useTenantStore()
const insets = useSafeAreaInsets() // 获取安全区信息
const scenicList = ref<any>([])
const currentTab = ref(0)
const currentScenic = ref<any>({})
const listData = ref<any>([])
onLoad(() => {
    getScenic()
})
const getScenic = async () => {
    const res: any = await getScenicList({ tenantId: tenantStore.tenantInfo.tenantId })
    if (res.code === 200) {
        scenicList.value = res.data
        currentScenic.value = res.data[0]
        scrollRef.value?.reload()
    }
}

const getScenicSpot = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getScenicSpotList({
            page: pageNo,
            pageSize: pageSize,
            tenantId: tenantStore.tenantInfo.tenantId,
            scenicId: currentScenic.value.id
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}
const tabsChange = ({ index }: any) => {
    currentScenic.value = scenicList.value[index]
    scrollRef.value?.reload()
}
// 监听页面滚动，需要时必填，在layouts的custom.vue中声明接收页面滚动事件
onPageScroll((e: any) => { })
const goDetail = (item: any) => {
    uni.navigateTo({
        url: `/pages/scenicSpot/detail?id=${item.id}&scenicId=${currentScenic.value.id}`
    })
}
</script>


<template>
    <view class="relative">
        <view class="flex h-[200px]">
            <wd-img src="https://educdn.xjzredu.cn/ticket/test/1/spot/20250722/W5eNQRwM1753155805012.jpg" width="100%"
                mode="aspectFill"></wd-img>
        </view>
        <view class="bg-[#f6f6f6] dark:bg-black rounded-t-2">
            <z-paging ref="scrollRef" :fixed="false" :use-page-scroll="true" v-model="listData" @query="getScenicSpot"
                :safe-area-inset-bottom="true" :use-safe-area-placeholder="true" :show-loading-more-no-more-view="false"
                :refresher-enabled="false" :load-more-enabled="true">
                <wd-sticky :offset-top="44 + insets.top">
                    <view class="w-vw">
                        <wd-tabs v-model="currentTab" @change="tabsChange" slidable="always" auto-line-width>
                            <wd-tab v-for="item in scenicList" :key="item.id" :title="item.scenicName">
                            </wd-tab>
                        </wd-tabs>
                    </view>
                </wd-sticky>
                <view class="p-3 bg-[#f6f6f6] dark:bg-black">
                    <view class="grid grid-cols-2 gap-3">
                        <view class="bg-white dark:bg-dark p-2 rounded" v-for="items in listData" :key="items.id"
                            @click="goDetail(items)">
                            <view>
                                <wd-img :src="items.spotCover" width="100%" height="150" radius="2"
                                    mode="aspectFill"></wd-img>
                            </view>
                            <view class="text-sm text-center">{{ items.spotName }}</view>
                        </view>
                    </view>
                </view>
            </z-paging>
        </view>
    </view>
</template>

<style lang="scss" scoped></style>
