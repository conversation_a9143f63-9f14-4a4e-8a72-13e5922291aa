<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "游玩卡"
    }
}</route>

<script lang="ts" setup>
import { getUserCardList } from '@/api/userCard';
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
const scrollRef = ref(null)
UseZPaging(scrollRef)
const listData = ref<any>([])
const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getUserCardList({
            page: pageNo,
            pageSize: pageSize,
            model: 1
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}
</script>

<template>
    <z-paging ref="scrollRef" v-model="listData" @query="getList" :safe-area-inset-bottom="true"
        :use-safe-area-placeholder="true" :show-loading-more-no-more-view="false">
        <view class="p-3">
            <view class="bg-white dark:bg-dark rounded mb-3 last:mb-0 p-2" v-for="item in listData" :key="item.id">
                <view class="text-sm flex justify-between items-center">
                    <view class="">{{ item.cardName }}</view>
                    <view class="text-gray-500">{{ item.validBeginDate }} 至 {{ item.validEndDate }}</view>
                </view>
                <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
                <view class="text-sm">
                    <view class="mb-1">有效期：{{ item.validBeginDate }} 至 {{ item.validEndDate }}</view>
                    <view class="mb-1">卡号：{{ item.cardNo }}</view>
                    <view class="mb-1">密码：{{ item.cardPassword }}</view>
                </view>
            </view>
        </view>
    </z-paging>
</template>

<style lang="scss" scoped></style>
