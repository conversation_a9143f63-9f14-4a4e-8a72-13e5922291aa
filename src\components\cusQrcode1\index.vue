<script lang="ts" setup>
import QRCode from 'qrcode'
import { ref, onMounted } from 'vue'

const props = withDefaults(defineProps<{
    value?: string
    size?: number
    color?: string
    backgroundColor?: string
    margin?: number
}>(), {
    value: 'https://www.baidu.com',
    size: 200,
    color: '#000000',
    backgroundColor: '#ffffff',
    margin: 10
})

const qrCodeDataUrl = ref('')
const errorMessage = ref('')
const canvasId = ref('')

onMounted(() => {
    canvasId.value = 'qrcode-canvas-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11)
    generateQRCode()
})

const generateQRCode = async () => {
    try {
        if (!props.value) {
            qrCodeDataUrl.value = ''
            return
        }

        errorMessage.value = ''
        const ocanvas = uni.createOffscreenCanvas({ type: '2d' })
        // 使用toDataURL方法生成二维码图片，避免直接操作canvas元素导致的兼容性问题
        const dataUrl = await QRCode.toDataURL(ocanvas, props.value, {
            width: props.size,
            margin: props.margin,
            color: {
                dark: props.color,
                light: props.backgroundColor
            }
        })

        qrCodeDataUrl.value = dataUrl
    } catch (error: any) {
        console.error('生成二维码失败:', error)
        errorMessage.value = error.message || '二维码生成失败'
    }
}

defineExpose({
    generateQRCode
})
</script>

<template>
    <view class="qr-code-container">
        <image v-if="qrCodeDataUrl" :src="qrCodeDataUrl"
            :style="{ width: props.size + 'px', height: props.size + 'px' }" mode="aspectFit" />
        <view v-if="errorMessage" class="error-message">
            {{ errorMessage }}
        </view>
    </view>
</template>

<style scoped>
.qr-code-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.error-message {
    color: #ff0000;
    font-size: 14px;
    padding: 10px;
    text-align: center;
}
</style>