<!-- 通过qrcode封装生成二维码 -->
<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import QRCode from 'qrcode'

// 定义组件属性
interface Props {
  /**
   * 二维码内容
   */
  text: string
  /**
   * 二维码尺寸
   * @default 200
   */
  size?: number
  /**
   * 二维码前景色
   * @default '#000000'
   */
  color?: string
  /**
   * 二维码背景色
   * @default '#ffffff'
   */
  backgroundColor?: string
  /**
   * 边距
   * @default 10
   */
  margin?: number
  /**
   * 是否失效
   * @default false
   */
  invalid?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 200,
  color: '#000000',
  backgroundColor: '#ffffff',
  margin: 1,
  invalid: false
})

// 二维码数据URL
const qrCodeDataUrl = ref('')
// 错误信息
const errorMessage = ref('')

/**
 * 生成二维码
 */
const generateQRCode = async () => {
  try {
    if (!props.text) {
      qrCodeDataUrl.value = ''
      errorMessage.value = ''
      return
    }

    // 清除之前的错误信息
    errorMessage.value = ''

    const offscreenCanvas = uni.createOffscreenCanvas({ type: '2d' })

    // 使用toDataURL方法生成二维码
    // 这是跨平台最兼容的方式
    qrCodeDataUrl.value = await QRCode.toDataURL(offscreenCanvas, props.text, {
      width: props.size,
      margin: 1,
      color: {
        dark: props.color,
        light: props.backgroundColor
      },
      // 禁用canvas相关选项，确保在所有平台都能正常工作
      errorCorrectionLevel: 'L',
      type: 'image/png'
    })
  } catch (error: any) {
    console.error('生成二维码失败:', error)
    errorMessage.value = error.message || '二维码生成失败'
    qrCodeDataUrl.value = ''
  }
}

// 监听属性变化，重新生成二维码
watch(() => [props.text, props.size, props.color, props.backgroundColor, props.margin, props.invalid], () => {
  generateQRCode()
}, { immediate: true })

// 组件挂载后生成二维码
onMounted(() => {
  nextTick(() => {
    generateQRCode()
  })
})

// 暴露方法供外部调用
defineExpose({
  generateQRCode
})
</script>

<template>
  <view class="qr-code-container">
    <view class="qr-code-wrapper">
      <image v-if="qrCodeDataUrl" :src="qrCodeDataUrl" :style="{ width: size + 'px', height: size + 'px' }"
        mode="aspectFit" />
      <view v-else-if="errorMessage" class="qr-code-error">
        {{ errorMessage }}
      </view>
      <view v-else class="qr-code-placeholder">
        二维码生成中...
      </view>
      <view v-if="invalid" class="invalid-overlay">
        <view class="invalid-text text-center flex flex-col items-center justify-center">
          <wd-icon name="close-circle-filled" custom-class="text-[50px]"></wd-icon>
          <text class="font-bold text-sm">
            二维码已失效</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.qr-code-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code-wrapper {
  position: relative;
}

.qr-code-placeholder,
.qr-code-error {
  width: v-bind('size + "px"');
  height: v-bind('size + "px"');
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

.qr-code-placeholder {
  background-color: #f5f5f5;
  color: #999;
}

.qr-code-error {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
  border-radius: 4px;
}

.invalid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  pointer-events: none;
}

.invalid-text {
  width: 100%;
  height: 100%;
  color: #ff4d4f;
}
</style>