<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "票务订单"
    }
}</route>

<script lang="ts" setup>
import { getTicketOrderList, getTicketOrderRefundList, cancelTicketOrder, deleteTicketOrder } from '@/api/ticket';
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
import { useMessage, useToast } from 'wot-design-uni';
import { useTenantStore, useEnumStore } from '@/store';
const tenantStore = useTenantStore()
const enumStore = useEnumStore()
const message = useMessage()
const toast = useToast()
const scrollRef = ref(null)
UseZPaging(scrollRef)
const listData = ref<any>([])

const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        let res: any
        if (currentStatus.value == 4) {
            res = await getTicketOrderRefundList({
                page: pageNo,
                pageSize: pageSize,
            })
        } else {
            res = await getTicketOrderList({
                page: pageNo,
                pageSize: pageSize,
                status: currentStatus.value
            })
        }
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}

const currentStatus = ref(0)
const statusList = ref(['全部', '待付款', '待使用', '已核销', '已退款'])
const tabsChange = ({ index }) => {
    currentStatus.value = index
    listData.value = []
    scrollRef.value?.reload()
}
// 详情
const goDetail = (item: any) => {
    uni.navigateTo({
        url: `/pages/ticketOrder/orderDetail?id=${item.id}`
    })
}

// 退款详情
const goRefundDetail = (item: any) => {
    uni.navigateTo({
        url: `/pages/ticketOrder/refundDetail?id=${item.id}`
    })
}
// 取消订单
const handleCancel = (item: any) => {
    message.confirm({
        msg: '确定要取消此订单吗？',
        title: '取消订单',
        confirmButtonText: '取消订单',
        confirmButtonProps: { type: 'error' }
    }).then(() => {
        cancelTicketOrder({
            orderId: item.id
        }).then(() => {
            toast.success('取消成功')
            scrollRef.value?.reload()
        })
        console.log('点击了确定按钮')
    })
        .catch(() => {
            console.log('点击了取消按钮')
        })
}
// 删除订单
const handleDelete = (item: any) => {
    message.confirm({
        msg: '确定要删除此订单吗？',
        title: '删除订单',
        confirmButtonText: '删除订单',
        confirmButtonProps: { type: 'error' }
    }).then(() => {
        deleteTicketOrder({
            orderId: item.id
        }).then(() => {
            toast.success('删除成功')
            scrollRef.value?.reload()
        })
        console.log('点击了确定按钮')
    })
        .catch(() => {
            console.log('点击了取消按钮')
        })
}

const filterStatus = (status: any) => {
    let obj = { label: '', color: 'default' }
    obj.label = enumStore.enumData?.ticketOrderStatus.list.find(item => item.value === status)?.label
    if ([1].includes(status)) {
        obj.color = 'text-warning'
    } else if ([2, 3, 4, 9].includes(status)) {
        obj.color = 'text-success'
    } else if ([8].includes(status)) {
        obj.color = 'text-danger'
    } else {
        obj.color = 'text-info'
    }
    return obj
}
</script>

<template>
    <z-paging ref="scrollRef" v-model="listData" @query="getList" :safe-area-inset-bottom="true"
        :use-safe-area-placeholder="true" :show-loading-more-no-more-view="false">
        <template #top>
            <wd-tabs v-model="currentStatus" @change="tabsChange">
                <wd-tab v-for="item in statusList" :key="item" :title="`${item}`"></wd-tab>
            </wd-tabs>
        </template>
        <view class="p-3" v-if="listData.length > 0">
            <view class="bg-white dark:bg-dark rounded mb-3 last:mb-0 p-2" v-for="item in listData" :key="item.id">
                <template v-if="currentStatus !== 4">
                    <view @click="goDetail(item)">
                        <view class="text-sm flex justify-between items-center">
                            <view class="">{{ item.scenicInfo.scenicName }}</view>
                            <view :class="`${filterStatus(item.orderStatus).color}`">{{
                                filterStatus(item.orderStatus).label }}
                            </view>
                        </view>
                        <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
                        <view class="flex justify-between mb-2">
                            <view class="flex gap-1.5 w-[75%]">
                                <view class="flex flex-shrink-0">
                                    <wd-img :src="item.orderItemList[0]?.ticketCover" width="80" height="80" radius="5"
                                        mode="aspectFill"></wd-img>
                                </view>
                                <view class="w-[65%]">
                                    <view class="mb-2 font-500 text-sm truncate">{{ item.orderItemList[0]?.ticketName }}
                                    </view>
                                    <view class="text-xs mb-1">下单时间：{{ item.createdAt }}</view>
                                    <view class="text-xs">门票数量：{{ item.orderItemList[0]?.ticketNum }}</view>
                                </view>
                            </view>
                            <view class="font-bold flex-shrink-0">￥{{ item.actualPrice }}</view>
                        </view>
                    </view>
                    <view class="flex justify-end gap-2">
                        <wd-button plain type="info" size="small" v-if="item.orderStatus == 1 && item.payStatus == 1"
                            @click="handleCancel(item)">取消</wd-button>
                        <wd-button type="error" size="small" plain
                            v-if="[10, 13, 14].includes(item.orderStatus) && [3, 4, 5, 6].includes(item.payStatus)"
                            @click="handleDelete(item)">删除</wd-button>
                        <wd-button type="warning" size="small"
                            v-if="item.orderStatus == 1 && item.payStatus == 1">去支付</wd-button>
                    </view>
                </template>
                <template v-else>
                    <view @click="goRefundDetail(item)">
                        <view class="text-sm flex justify-between items-center">
                            <view class="">{{ item.scenicInfo.scenicName }}</view>
                            <view :class="`text-${filterStatus(item.orderStatus).color}`">
                                {{ filterStatus(item.orderStatus).label }}
                            </view>
                        </view>
                        <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
                        <view class="flex justify-between mb-2">
                            <view class="flex gap-1.5">
                                <view class="w-full">
                                    <view class="mb-2 font-500 text-sm truncate">{{ item.refundItemList[0]?.ticketName
                                    }}
                                    </view>
                                    <view class="text-xs mb-1">申请时间：{{ item.createdAt }}</view>
                                    <view class="text-xs">退票数量：{{ item.refundItemList[0]?.refundNum }}</view>
                                </view>
                            </view>
                            <view class="font-bold flex-shrink-0">￥{{ item.refundPrice }}</view>
                        </view>
                    </view>
                </template>
            </view>
        </view>
    </z-paging>
</template>

<style lang="scss" scoped></style>
