<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "订单详情"
    }
}</route>

<script lang="ts" setup>
import { getTicketOrderInfo, cancelTicketOrder, deleteTicketOrder } from '@/api/ticket'
import { useEnumStore } from '@/store'
import cusQrcode from '@/components/cusQrCode/index.vue'
import { useMessage, useToast } from 'wot-design-uni';
const message = useMessage()
const toast = useToast()
const enumStore = useEnumStore()
onLoad((options) => {
    getOrderData(options.id)
})
const orderData = ref<any>({})
const orderTicketList = ref<any>(null)
const useNum = ref(0)
const noUseNum = ref(0)
const refundNum = ref(0)
const getOrderData = async (id: any) => {
    const res: any = await getTicketOrderInfo({
        orderId: id,
    })
    if (res.code === 200) {
        orderData.value = res.data
        orderTicketList.value = res.data.orderItemList[0]
        useNum.value = res.data.orderItemList[0].ticketOrderDetailList.filter(item => item.orderStatus == 5).length
        noUseNum.value = res.data.orderItemList[0].ticketOrderDetailList.filter(item => item.orderStatus == 2).length
        refundNum.value = res.data.orderItemList[0].ticketOrderDetailList.filter(item => item.orderStatus == 7).length
        console.log(res.data)
    }
}
const showAmount = ref(false)
const showAmountDetail = () => {
    showAmount.value = true
}
const showUseDetail = () => {

}

// 取消订单
const handleCancel = (item: any) => {
    message.confirm({
        msg: '确定要取消此订单吗？',
        title: '取消订单',
        confirmButtonText: '取消订单',
        confirmButtonProps: { type: 'error' }
    }).then(() => {
        cancelTicketOrder({
            orderId: item.id
        }).then(() => {
            toast.success('取消成功')
            uni.navigateBack()
        })
        console.log('点击了确定按钮')
    })
        .catch(() => {
            console.log('点击了取消按钮')
        })
}
// 删除订单
const handleDelete = (item: any) => {
    message.confirm({
        msg: '确定要删除此订单吗？',
        title: '删除订单',
        confirmButtonText: '删除订单',
        confirmButtonProps: { type: 'error' }
    }).then(() => {
        deleteTicketOrder({
            orderId: item.id
        }).then(() => {
            toast.success('删除成功')
            uni.navigateBack()
        })
        console.log('点击了确定按钮')
    })
        .catch(() => {
            console.log('点击了取消按钮')
        })
}

// 继续支付
const goPay = () => {
    console.log('继续支付')
}
// 申请退款
const goRefund = () => {
    console.log('申请退款')
}

// 查看退款进度
const goRefundProgress = () => {
    console.log('查看退款进度详情')
    uni.navigateTo({
        url: `/pages/ticketOrder/refundProgress?id=${orderData.value.id}`
    })
}

const filterStatus = (status: any) => {
    let obj = { label: '', color: 'default' }
    obj.label = enumStore.enumData?.ticketOrderStatus.list.find(item => item.value === status)?.label
    if ([1].includes(status)) {
        obj.color = 'text-warning'
    } else if ([2, 3, 4, 9].includes(status)) {
        obj.color = 'text-success'
    } else if ([8].includes(status)) {
        obj.color = 'text-danger'
    } else {
        obj.color = 'text-info'
    }
    return obj
}

const filterText = (arr: any, val: any) => {
    return arr.find(item => item.value === val)?.label
}

const filterValidDate = (type: any, status: any) => {
    if (type == 1) {
        return orderTicketList.value.validBeginDate + ' 当天有效'
    } else if (type == 4) {
        return status == 9 ? orderTicketList.value.validBeginDate
            ? orderTicketList.value.validBeginDate + '~' + orderTicketList.value.validEndDate
            : '' : `激活后${orderTicketList.value.validDayNum}天有效`
    } else {
        return orderTicketList.value.validBeginDate
            ? orderTicketList.value.validBeginDate + '~' + orderTicketList.value.validEndDate
            : ''
    }
}

const copyNo = (no: string) => {
    uni.setClipboardData({
        data: no
    })
}

const showCode = (status: any) => {
    if ([2, 3, 4, 5, 6, 7, 8, 9].includes(status)) {
        return true
    }
    return false
}
</script>

<template>
    <view class="p-3 pb-safe">
        <view class="bg-white dark:bg-dark rounded px-2 py-3 mb-3" v-if="[6, 7].includes(orderData.orderStatus)">
            <view class="text-sm flex justify-between items-center">
                <view class="font-bold">查看退款进度</view>
                <view class="text-gray-500" @click="goRefundProgress">查看详情<wd-icon name="arrow-right"
                        size="22px"></wd-icon>
                </view>
            </view>
        </view>
        <view class="bg-white dark:bg-dark rounded p-2 mb-3">
            <view class="text-sm flex justify-between items-center">
                <view class="font-bold">票类信息</view>
                <view :class="`${filterStatus(orderData.orderStatus).color}`">{{
                    filterStatus(orderData.orderStatus).label }}
                </view>
            </view>
            <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
            <view class="h-[270px] mb-2"
                v-if="orderTicketList && showCode(orderData.orderStatus) && (orderTicketList.model == 1 || orderTicketList.model == 2)">
                <swiper>
                    <!-- 一票一码 -->
                    <template v-if="orderTicketList.qrcodeRule == 1">
                        <swiper-item v-for="item in orderTicketList.ticketOrderDetailList" :key="item.id">
                            <view class="mx-1">
                                <view class="font-bold mb-2 text-center">{{ item.ticketName }}</view>
                                <cusQrcode :text="item.verificationCode" :size="150" :margin="0"
                                    :invalid="item.orderStatus == 2 ? false : true"></cusQrcode>
                                <view class="text-center text-sm my-2">
                                    <view :class="item.orderStatus == 2 ? '' : 'line-through'">核销码：{{
                                        item.verificationCode }}</view>
                                    <view>出行人：{{ item.touristName }}
                                    </view>
                                    <view>身份证：{{ item.touristIdcard }}</view>
                                </view>
                            </view>
                        </swiper-item>
                    </template>
                    <!-- 多票一码，只需显示一个 -->
                    <template v-else>
                        <swiper-item>
                            <view class="mx-1">
                                <view class="font-bold mb-2 text-center">{{ orderTicketList.ticketName }}</view>
                                <cusQrcode :text="orderTicketList.ticketOrderDetailList[0].verificationCode" :size="150"
                                    :margin="0"></cusQrcode>
                                <view class="text-center text-sm mb-2"
                                    :class="orderTicketList.ticketOrderDetailList[0].orderStatus !== 2 ? 'line-through' : ''">
                                    核销码：{{
                                        orderTicketList.ticketOrderDetailList[0].verificationCode }}</view>
                                <view class="text-center text-sm mb-2">出行人：{{
                                    orderTicketList.ticketOrderDetailList[0].touristName }}</view>
                                <view class="text-center text-sm mb-2">身份证：{{
                                    orderTicketList.ticketOrderDetailList[0].touristIdcard }}</view>
                            </view>
                        </swiper-item>
                    </template>
                </swiper>
            </view>
            <view class="mb-3" v-else>
                <view class="bg-[#f6f6f6] dark:bg-black rounded-lg p-2 mb-2"
                    v-for="item in orderTicketList?.ticketOrderDetailList" :key="item.id">
                    <view class="font-bold mb-2 text-center">{{ item.ticketName }}</view>
                    <view class="text-center text-sm">
                        <view class="mb-2">出行人：{{ item.touristName }}</view>
                        <view class="mb-2">身份证：{{ item.touristIdcard }}</view>
                        <view class="mb-2">有效期：{{ filterValidDate(orderTicketList.validType, item.orderStatus) }}</view>
                    </view>
                    <view class="text-center"><wd-button>{{ item.orderStatus == 9 ? '去使用' : '去激活' }}</wd-button>
                    </view>
                </view>
            </view>
            <view class="px-1 text-sm" v-if="orderTicketList">
                <view class="mb-1" v-if="orderTicketList?.model == 1 || orderTicketList?.model == 2">
                    <text class="text-gray-500 mr-2">游玩日期</text>{{ orderTicketList.playDate }}
                </view>
                <view class="mb-1" v-if="orderTicketList?.model == 1 || orderTicketList?.model == 2">
                    <text class="text-gray-500 mr-2">有效日期</text>{{
                        orderTicketList.validType == 1
                            ? orderTicketList.validBeginDate + ' 当天有效'
                            : orderTicketList.validBeginDate
                                ? orderTicketList.validBeginDate + '~' + orderTicketList.validEndDate
                                : ''
                    }}
                </view>
                <view class="mb-1">
                    <text class="text-gray-500 mr-2">入园时间</text>{{ orderTicketList.entryBeginTime + ' - ' +
                        orderTicketList.entryEndTime }}
                </view>
                <view class="mb-1">
                    <text class="text-gray-500 mr-2">入园地址</text>{{ orderTicketList.entryAddress }}
                </view>
                <view class="mb-1">
                    <text class="text-gray-500 mr-2">入园方式</text>{{ orderTicketList.entryMethod }}
                </view>
                <view>
                    <text class="text-gray-500 mr-2">退票规则</text>{{
                        orderTicketList.refundType == 4 ? orderTicketList.refundDay + '天内可退' :
                            filterText(enumStore.enumData?.ticketRefundType.list,
                                orderTicketList.refundType) }}
                </view>
            </view>
        </view>
        <view class="bg-white dark:bg-dark rounded p-2 mb-3">
            <view class="text-sm flex justify-between items-center">
                <view class="font-bold">订单信息</view>
                <view class="text-gray-500" @click="showAmountDetail">金额明细<wd-icon name="arrow-right"
                        size="22px"></wd-icon>
                </view>
            </view>
            <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
            <view class="text-sm px-1">
                <view class="flex items-center mb-1">
                    <view class="w-[17%] mr-2 text-gray-500" style="text-align-last: justify">订单号</view>{{
                        orderData.orderNo
                    }}
                    <wd-tag round custom-class="ml-2 !px-[5px] !py-[2px]" @click="copyNo(orderData.orderNo)">复制</wd-tag>
                </view>
                <template>
                    <view class="flex items-center mb-1">
                        <view class="w-[17%] mr-2 text-gray-500" style="text-align-last: justify">购票数量</view>{{
                            orderTicketList?.ticketNum }}张
                    </view>
                    <view class="flex items-center mb-1">
                        <view class="w-[17%] mr-2 text-gray-500" style="text-align-last: justify">支付金额</view>
                        ￥{{ orderData.actualPrice }}
                    </view>
                    <view class="flex items-center mb-1">
                        <view class="w-[17%] mr-2 text-gray-500" style="text-align-last: justify">支付方式</view>{{
                            filterText(enumStore.enumData?.orderPayMethod.list, orderData.payMethod) }}
                    </view>
                </template>
                <view class="flex items-center mb-1">
                    <view class="w-[17%] mr-2 text-gray-500" style="text-align-last: justify">下单时间</view>{{
                        orderData.orderTime }}
                </view>
            </view>
        </view>
        <view class="bg-white dark:bg-dark rounded p-2 mb-3"
            v-if="showCode(orderData.orderStatus) && (orderTicketList.model == 1 || orderTicketList.model == 2)">
            <view class="text-sm flex justify-between items-center">
                <view class="font-bold">使用状况</view>
                <view class="text-gray-500" @click="showUseDetail">详情<wd-icon name="arrow-right" size="22px"></wd-icon>
                </view>
            </view>
            <wd-divider custom-class="!my-2 !p-0" color="rgba(0,0,0,0.1)"></wd-divider>
            <view class="flex justify-between items-center bg-[#f6f6f6] dark:bg-dark rounded-lg px-4 py-2">
                <view class="flex-1 text-center">
                    <text class="mb-1 text-gray-500">已使用</text><br>
                    <text class="font-bold" :class="useNum > 0 ? 'text-primary' : ''">{{ useNum }}</text>
                </view>
                <view class="flex-1 text-center">
                    <text class="mb-1 text-gray-500">未使用 </text><br>
                    <text class="font-bold" :class="noUseNum > 0 ? 'text-primary' : ''">{{ noUseNum }}</text>
                </view>
                <view class="flex-1 text-center">
                    <text class="mb-1 text-gray-500">退款</text> <br>
                    <text class="font-bold" :class="refundNum > 0 ? 'text-primary' : ''">{{ refundNum }}</text>
                </view>
            </view>
        </view>
        <!-- 占位用 -->
        <view class="h-[60px]"
            v-if="[1, 10, 13, 14].includes(orderData.orderStatus) && [1, 3, 4, 5, 6].includes(orderData.payStatus)">
        </view>
        <!-- 操作区 -->
        <view class="fixed bottom-0 left-0 w-full bg-white dark:bg-dark text-right pb-safe"
            v-if="[1, 10, 13, 14].includes(orderData.orderStatus) && [1, 3, 4, 5, 6].includes(orderData.payStatus)">
            <view class="p-3 flex justify-end gap-2">
                <wd-button plain type="info" v-if="orderData.orderStatus == 1 && orderData.payStatus == 1"
                    @click="handleCancel(orderData)">取消</wd-button>
                <wd-button type="error"
                    v-if="[10, 13, 14].includes(orderData.orderStatus) && [3, 4, 5, 6].includes(orderData.payStatus)"
                    @click="handleDelete(orderData)">删除</wd-button>
                <wd-button type="warning" v-if="orderData.orderStatus == 1 && orderData.payStatus == 1"
                    @click="goPay">去支付</wd-button>
                <wd-button type="warning" plain v-if="orderData.orderStatus == 2" @click="goRefund">申请退款</wd-button>
            </view>
        </view>
    </view>
    <wd-action-sheet v-model="showAmount" title="金额明细" @close="showAmount = false">
        <view class="px-3 text-sm">
            <view class="mb-1">
                <text class="text-gray-500 mr-2">订单原价</text>
                <text>￥{{ orderData.orderPrice }}</text>
            </view>
            <view class="mb-1">
                <text class="text-gray-500 mr-2">优惠金额</text>
                <text class="text-danger">- ￥{{ orderData.discountPrice }}</text>
            </view>
            <view class="mb-1">
                <text class="text-gray-500 mr-2">实付金额</text>
                <text>￥{{ orderData.actualPrice }}</text>
            </view>
        </view>
    </wd-action-sheet>
</template>

<style lang="scss" scoped></style>
